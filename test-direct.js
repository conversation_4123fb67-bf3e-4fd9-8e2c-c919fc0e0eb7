// Direct test of the MCP tool handlers
// This bypasses the MCP protocol and tests the handlers directly

const fs = require('fs');
const path = require('path');

// Import the compiled handler
const { handleToolRequest } = require('./game-state-server/dist/index.js');

async function testDirectly() {
  console.log('🧪 Testing MCP tool handlers directly...\n');
  
  const results = [];
  let testCounter = 0;
  
  async function testTool(toolName, args, expectedPattern, description) {
    testCounter++;
    console.log(`\n🔧 Test ${testCounter}: ${toolName} - ${description}`);
    
    try {
      const request = {
        params: {
          name: toolName,
          arguments: args
        }
      };
      
      const result = await handleToolRequest(request);
      const success = !result.isError;
      const output = result.content?.map(c => c.text || JSON.stringify(c)).join('\n') || 'No output';
      
      results.push({
        test: testCounter,
        tool: toolName,
        description,
        args,
        success,
        output: output.substring(0, 200) + (output.length > 200 ? '...' : ''),
        error: result.isError ? output : null
      });
      
      console.log(success ? '✅ PASS' : '❌ FAIL');
      if (!success) {
        console.log(`Error: ${output}`);
      } else {
        console.log(`Output: ${output.substring(0, 100)}${output.length > 100 ? '...' : ''}`);
      }
      
      return { success, result, output };
    } catch (error) {
      results.push({
        test: testCounter,
        tool: toolName,
        description,
        args,
        success: false,
        output: null,
        error: error.message
      });
      
      console.log(`❌ ERROR: ${error.message}`);
      return { success: false, error: error.message };
    }
  }
  
  // Test 1: Create a character with unique name
  const uniqueName = 'TestChar_' + Date.now();
  const charResult = await testTool('create_character', {
    name: uniqueName,
    game_line: 'vampire',
    concept: 'Test Subject',
    clan: 'Brujah'
  }, 'Character', 'Create a test character');
  
  let characterId = null;
  if (charResult.success) {
    // Try to extract character ID from output
    const idMatch = charResult.output.match(/ID:\s*(\d+)/);
    if (idMatch) {
      characterId = parseInt(idMatch[1]);
      console.log(`📝 Extracted character ID: ${characterId}`);
    }
  }
  
  // Test 2: List characters
  await testTool('list_characters', {}, 'Character', 'List all characters');
  
  // Test 3: Get character (if we have an ID)
  if (characterId) {
    await testTool('get_character', { character_id: characterId }, 'Character', 'Get character by ID');
  }
  
  // Test 4: Update character (if we have an ID)
  if (characterId) {
    await testTool('update_character', {
      character_id: characterId,
      updates: { willpower_current: 3, concept: 'Updated Test' }
    }, 'updated', 'Update character');
  }
  
  // Test 5: Status effects (if we have an ID)
  if (characterId) {
    const statusResult = await testTool('apply_status_effect', {
      target_type: 'character',
      target_id: characterId,
      effect_name: 'Test Effect',
      description: 'A test status effect'
    }, 'Status effect', 'Apply status effect');
    
    // Extract effect ID if successful
    let effectId = null;
    if (statusResult.success) {
      const effectMatch = statusResult.output.match(/ID:\s*(\d+)/);
      if (effectMatch) {
        effectId = parseInt(effectMatch[1]);
      }
    }
    
    // Test removing the effect
    if (effectId) {
      await testTool('remove_status_effect', {
        effect_id: effectId
      }, 'removed', 'Remove status effect');
    }
    
    // Test listing effects
    await testTool('get_status_effects', {
      target_type: 'character',
      target_id: characterId
    }, 'effects', 'List status effects');
  }
  
  // Test 6: Validation errors
  await testTool('create_character', {
    game_line: 'vampire'
    // Missing name
  }, 'Error', 'Test missing name validation');
  
  await testTool('create_character', {
    name: 'Test',
    game_line: 'invalid'
  }, 'Error', 'Test invalid game_line validation');
  
  // Test 7: Duplicate name
  await testTool('create_character', {
    name: uniqueName, // Same name as before
    game_line: 'vampire'
  }, 'UNIQUE constraint', 'Test duplicate name constraint');
  
  console.log('\n📊 Test Summary:');
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  // Save detailed results
  fs.writeFileSync('test-results-direct.json', JSON.stringify(results, null, 2));
  console.log('\n📄 Detailed results saved to test-results-direct.json');
  
  return { passed, total, results };
}

testDirectly().catch(console.error);
