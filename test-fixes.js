// Simple test script to verify our fixes work
// This tests the core functionality without the complex MCP protocol

const { GameDatabase } = require('./game-state-server/dist/db.js');

function testFixes() {
  console.log('🧪 Testing fixes...\n');
  
  try {
    // Initialize database
    const db = new GameDatabase();
    console.log('✅ Database initialized');
    
    // Test 1: Create a character
    console.log('\n📝 Test 1: Character Creation');
    const testChar = {
      name: 'TestCharacter_' + Date.now(),
      game_line: 'vampire',
      concept: 'Test Subject',
      clan: 'Brujah',
      strength: 2,
      dexterity: 3,
      stamina: 2
    };
    
    const character = db.createCharacter(testChar);
    if (character) {
      console.log(`✅ Character created: ${character.name} [ID: ${character.id}]`);
    } else {
      console.log('❌ Character creation failed');
      return;
    }
    
    // Test 2: Retrieve character
    console.log('\n📖 Test 2: Character Retrieval');
    const retrieved = db.getCharacterById(character.id);
    if (retrieved && retrieved.name === character.name) {
      console.log(`✅ Character retrieved: ${retrieved.name}`);
    } else {
      console.log('❌ Character retrieval failed');
    }
    
    // Test 3: List characters
    console.log('\n📋 Test 3: List Characters');
    const characters = db.listCharacters();
    if (characters.length > 0) {
      console.log(`✅ Found ${characters.length} character(s)`);
      characters.forEach(char => {
        console.log(`  - ${char.name} (${char.game_line}) [ID: ${char.id}]`);
      });
    } else {
      console.log('❌ No characters found');
    }
    
    // Test 4: Status effects
    console.log('\n🌀 Test 4: Status Effects');
    const effectId = db.addStatusEffect({
      target_type: 'character',
      target_id: character.id,
      effect_name: 'Test Effect',
      description: 'A test status effect',
      mechanical_effect: { test: true },
      duration_type: 'rounds',
      duration_value: 3
    });
    
    if (effectId) {
      console.log(`✅ Status effect applied [ID: ${effectId}]`);
      
      // List effects
      const effects = db.listStatusEffects('character', character.id);
      console.log(`✅ Found ${effects.length} status effect(s)`);
      
      // Remove effect
      const removed = db.removeStatusEffect(effectId);
      if (removed) {
        console.log('✅ Status effect removed');
      } else {
        console.log('❌ Status effect removal failed');
      }
    } else {
      console.log('❌ Status effect creation failed');
    }
    
    // Test 5: Update character
    console.log('\n✏️ Test 5: Character Update');
    const updated = db.updateCharacter(character.id, {
      willpower_current: 3,
      concept: 'Updated Test Subject'
    });
    
    if (updated && updated.concept === 'Updated Test Subject') {
      console.log('✅ Character updated successfully');
    } else {
      console.log('❌ Character update failed');
    }
    
    // Test 6: Duplicate name constraint
    console.log('\n🚫 Test 6: Duplicate Name Constraint');
    try {
      db.createCharacter({
        name: character.name, // Same name
        game_line: 'vampire'
      });
      console.log('❌ Duplicate name should have failed');
    } catch (error) {
      if (error.message.includes('UNIQUE constraint failed')) {
        console.log('✅ Duplicate name properly rejected');
      } else {
        console.log(`❌ Unexpected error: ${error.message}`);
      }
    }
    
    console.log('\n🎉 All tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

testFixes();
