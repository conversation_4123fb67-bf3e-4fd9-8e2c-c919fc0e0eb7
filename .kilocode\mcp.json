{"mcpServers": {"rpg-game-state": {"name": "rpg-game-state-server", "command": "node", "args": ["dist/index.js"], "cwd": "E:\\Tinker\\rpg-mcp-servers\\game-state-server", "enabled": true, "alwaysAllow": ["add_item", "advance_turn", "apply_damage", "apply_status_effect", "award_xp", "create_antagonist", "create_character", "gain_resource", "get_antagonist", "get_character", "get_character_by_name", "get_current_turn", "get_initiative_order", "get_inventory", "get_status_effects", "get_trait_improvement_cost", "get_world_state", "improve_trait", "list_antagonists", "list_characters", "remove_antagonist", "remove_item", "remove_status_effect", "restore_resource", "save_story_progress", "save_world_state", "set_initiative", "spend_resource", "spend_xp", "update_antagonist", "update_character", "update_item"]}, "rpg-combat-engine": {"name": "rpg-combat-engine-server", "command": "node", "args": ["dist/index.js"], "cwd": "E:\\Tinker\\rpg-mcp-servers\\combat-engine-server", "enabled": true, "alwaysAllow": ["advance_turn", "change_form", "get_current_turn", "get_initiative_order", "invoke_cantrip", "roll_contested_action", "roll_damage_pool", "roll_magick_effect", "roll_soak", "roll_social_combat", "roll_virtue_check", "roll_wod_pool", "set_initiative", "spend_rage_for_extra_actions"]}}}