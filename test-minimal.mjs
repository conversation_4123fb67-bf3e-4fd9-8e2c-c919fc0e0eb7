// Minimal test using ES modules
// This tests the core functionality by creating a simple database test

import Database from 'better-sqlite3';
import { join } from 'path';
import { existsSync, mkdirSync } from 'fs';

console.log('🧪 Running minimal database test...\n');

try {
  // Create data directory
  const DATA_DIR = join(process.cwd(), 'data');
  if (!existsSync(DATA_DIR)) {
    mkdirSync(DATA_DIR, { recursive: true });
  }
  
  const DB_PATH = join(DATA_DIR, 'test-game-state.db');
  const db = new Database(DB_PATH);
  
  console.log('✅ Database connection established');
  
  // Create basic character table
  db.exec(`
    CREATE TABLE IF NOT EXISTS characters (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      concept TEXT,
      game_line TEXT NOT NULL,
      strength INTEGER DEFAULT 1,
      dexterity INTEGER DEFAULT 1,
      stamina INTEGER DEFAULT 1,
      charisma INTEGER DEFAULT 1,
      manipulation INTEGER DEFAULT 1,
      appearance INTEGER DEFAULT 1,
      perception INTEGER DEFAULT 1,
      intelligence INTEGER DEFAULT 1,
      wits INTEGER DEFAULT 1,
      willpower_current INTEGER DEFAULT 1,
      willpower_permanent INTEGER DEFAULT 1,
      health_levels TEXT NOT NULL,
      experience INTEGER DEFAULT 0
    )
  `);
  
  console.log('✅ Character table created');
  
  // Test character creation
  const uniqueName = 'TestChar_' + Date.now();
  const stmt = db.prepare(`
    INSERT INTO characters (
      name, concept, game_line, strength, dexterity, stamina,
      charisma, manipulation, appearance, perception, intelligence, wits,
      willpower_current, willpower_permanent, health_levels, experience
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);
  
  const health_levels = JSON.stringify({ 
    bruised: 0, hurt: 0, injured: 0, wounded: 0, 
    mauled: 0, crippled: 0, incapacitated: 0 
  });
  
  const result = stmt.run(
    uniqueName, 'Test Subject', 'vampire', 2, 3, 2,
    3, 2, 2, 2, 2, 2, 5, 5, health_levels, 0
  );
  
  const characterId = result.lastInsertRowid;
  console.log(`✅ Character created with ID: ${characterId}`);
  
  // Test character retrieval
  const getStmt = db.prepare('SELECT * FROM characters WHERE id = ?');
  const character = getStmt.get(characterId);
  
  if (character && character.name === uniqueName) {
    console.log(`✅ Character retrieved: ${character.name}`);
  } else {
    console.log('❌ Character retrieval failed');
  }
  
  // Test character listing
  const listStmt = db.prepare('SELECT id, name, game_line FROM characters');
  const characters = listStmt.all();
  console.log(`✅ Found ${characters.length} character(s) in database`);
  
  // Test character update
  const updateStmt = db.prepare('UPDATE characters SET willpower_current = ?, concept = ? WHERE id = ?');
  const updateResult = updateStmt.run(3, 'Updated Test Subject', characterId);
  
  if (updateResult.changes > 0) {
    console.log('✅ Character updated successfully');
  } else {
    console.log('❌ Character update failed');
  }
  
  // Test duplicate name constraint
  try {
    stmt.run(
      uniqueName, 'Duplicate', 'vampire', 1, 1, 1,
      1, 1, 1, 1, 1, 1, 1, 1, health_levels, 0
    );
    console.log('❌ Duplicate name should have failed');
  } catch (error) {
    if (error.message.includes('UNIQUE constraint failed')) {
      console.log('✅ Duplicate name properly rejected');
    } else {
      console.log(`❌ Unexpected error: ${error.message}`);
    }
  }
  
  // Create status effects table
  db.exec(`
    CREATE TABLE IF NOT EXISTS status_effects (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      character_id INTEGER,
      npc_id INTEGER,
      effect_name TEXT NOT NULL,
      description TEXT,
      mechanical_effect TEXT,
      duration_type TEXT DEFAULT 'indefinite',
      duration_value INTEGER,
      FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
    )
  `);
  
  console.log('✅ Status effects table created');
  
  // Test status effect creation
  const effectStmt = db.prepare(`
    INSERT INTO status_effects (character_id, effect_name, description, mechanical_effect, duration_type, duration_value)
    VALUES (?, ?, ?, ?, ?, ?)
  `);
  
  const effectResult = effectStmt.run(
    characterId, 'Test Effect', 'A test status effect', 
    JSON.stringify({ test: true }), 'rounds', 3
  );
  
  const effectId = effectResult.lastInsertRowid;
  console.log(`✅ Status effect created with ID: ${effectId}`);
  
  // Test status effect retrieval
  const getEffectStmt = db.prepare('SELECT * FROM status_effects WHERE character_id = ?');
  const effects = getEffectStmt.all(characterId);
  console.log(`✅ Found ${effects.length} status effect(s)`);
  
  // Test status effect removal
  const removeEffectStmt = db.prepare('DELETE FROM status_effects WHERE id = ?');
  const removeResult = removeEffectStmt.run(effectId);
  
  if (removeResult.changes > 0) {
    console.log('✅ Status effect removed successfully');
  } else {
    console.log('❌ Status effect removal failed');
  }
  
  db.close();
  console.log('\n🎉 All database tests passed!');
  
  // Clean up test database
  try {
    import('fs').then(({ unlinkSync }) => {
      unlinkSync(DB_PATH);
      console.log('🧹 Test database cleaned up');
    }).catch(() => {
      console.log('⚠️ Could not clean up test database');
    });
  } catch (e) {
    console.log('⚠️ Could not clean up test database');
  }
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
  process.exit(1);
}
